"""
Логика отправки вопросов и обработки таймаутов
Основная логика quiz системы для работы с вопросами
"""

from aiogram import Bot
from aiogram.fsm.context import FSMContext
from datetime import datetime, timedelta
import logging
import asyncio
import uuid
from typing import Callable, Optional

from database import (
    QuestionRepository, AnswerOptionRepository, BonusAnswerOptionRepository
)
from .state_manager import active_questions, completed_questions


async def send_next_question(chat_id: int, state: FSMContext, bot: Bot, finish_callback: Optional[Callable] = None):
    """Универсальная функция отправки следующего вопроса"""
    data = await state.get_data()
    index = data.get("q_index", 0)
    questions = data.get("questions", [])

    # Проверяем валидность данных состояния
    if not data or not questions:
        await bot.send_message(
            chat_id,
            "❌ Данные теста потеряны. Пожалуйста, начните тест заново из меню."
        )
        await state.clear()
        return

    if index >= len(questions):
        # Завершаем тест
        if finish_callback:
            await finish_callback(chat_id, state, bot)
        return
    
    question_data = questions[index]
    question_id = question_data['id']
    
    # Определяем тип теста и получаем варианты ответов
    # Проверяем, есть ли в данных состояния информация о бонусном тесте
    is_bonus_test = data.get("bonus_test_id") is not None

    if is_bonus_test:
        # Для бонусных тестов используем BonusAnswerOptionRepository
        answer_options = await BonusAnswerOptionRepository.get_by_bonus_question(question_id)
    else:
        # Для обычных тестов используем AnswerOptionRepository
        answer_options = await AnswerOptionRepository.get_by_question(question_id)

    if not answer_options:
        error_msg = f"❌ QUIZ: Варианты ответов не найдены для вопроса ID {question_id}"
        logging.error(error_msg)
        await bot.send_message(chat_id, "❌ Ошибка: варианты ответов не найдены")
        return
    
    # Сортируем варианты по порядковому номеру
    answer_options.sort(key=lambda x: x.order_number)
    
    # Формируем список вариантов ответов и находим правильный
    options = []
    correct_option_id = None
    
    for i, option in enumerate(answer_options):
        options.append(option.text)
        if option.is_correct:
            correct_option_id = i
    
    if correct_option_id is None:
        error_msg = f"❌ QUIZ: Правильный ответ не найден для вопроса ID {question_id}"
        logging.error(error_msg)
        await bot.send_message(chat_id, "❌ Ошибка: правильный ответ не найден")
        return
    
    # Генерируем уникальный ID для этого вопроса
    question_uuid = str(uuid.uuid4())

    # Сохраняем информацию о текущем вопросе (без времени начала)
    await state.update_data(
        current_question_id=question_id,
        current_question_uuid=question_uuid,
        current_answer_options=[{
            'id': opt.id,
            'text': opt.text,
            'is_correct': opt.is_correct,
            'order_number': opt.order_number
        } for opt in answer_options],
        question_answered=False
    )

    # Формируем текст вопроса
    question_text = question_data['text']
    photo_message = None

    if question_data['photo_path']:
        # Если есть фото, сначала отправляем его
        photo_message = await bot.send_photo(
            chat_id=chat_id,
            photo=question_data['photo_path'],
        )

    # Фиксируем время начала вопроса непосредственно перед отправкой опроса
    question_start_time = datetime.now()

    # Обновляем время начала в состоянии
    await state.update_data(question_start_time=question_start_time.isoformat())

    # Регистрируем активный вопрос
    active_questions[question_uuid] = {
        "chat_id": chat_id,
        "state": state,
        "bot": bot,
        "answered": False,
        "question_id": question_id,
        "start_time": question_start_time,
        "finish_callback": finish_callback
    }

    # Вычисляем close_date на основе актуального времени начала вопроса
    close_date = int((question_start_time + timedelta(seconds=question_data['time_limit'])).timestamp())

    poll_message = await bot.send_poll(
        chat_id=chat_id,
        question=f"{question_text}",
        options=options,
        type="quiz",
        correct_option_id=correct_option_id,
        is_anonymous=False,
        close_date=close_date
    )
    
    # Сохраняем ID сообщений для последующего удаления
    data = await state.get_data()
    messages_to_delete = data.get("messages_to_delete", [])
    
    if photo_message:
        messages_to_delete.append(photo_message.message_id)
    messages_to_delete.append(poll_message.message_id)
    
    # Сохраняем ID сообщения с опросом
    await state.update_data(
        current_poll_message_id=poll_message.message_id,
        messages_to_delete=messages_to_delete
    )
    
    # Запускаем надежный таймер для обработки таймаута
    asyncio.create_task(handle_question_timeout_reliable(
        question_uuid, question_data['time_limit'], finish_callback
    ))


async def handle_question_timeout_reliable(question_uuid: str, timeout_seconds: int, finish_callback: Optional[Callable] = None):
    """Надежная обработка таймаута вопроса через уникальный UUID"""
    try:
        await asyncio.sleep(timeout_seconds)
        
        # Проверяем, что вопрос еще активен
        if question_uuid not in active_questions:
            return
        
        question_info = active_questions[question_uuid]
        
        # Проверяем, был ли уже дан ответ
        if question_info["answered"]:
            del active_questions[question_uuid]
            return
        
        # Обрабатываем таймаут
        await process_question_timeout_reliable(question_uuid, finish_callback)
        
    except Exception as e:
        logging.error(f"❌ QUIZ: Ошибка в обработчике таймаута для {question_uuid}: {e}")
        if question_uuid in active_questions:
            del active_questions[question_uuid]


async def process_question_timeout_reliable(question_uuid: str, finish_callback: Optional[Callable] = None):
    """Надежная обработка таймаута вопроса"""
    try:
        if question_uuid not in active_questions:
            logging.error(f"❌ QUIZ: Вопрос {question_uuid} не найден в активных")
            return

        question_info = active_questions[question_uuid]
        chat_id = question_info["chat_id"]
        state = question_info["state"]
        bot = question_info["bot"]

        # Отмечаем как завершенный
        completed_questions.add(question_uuid)

        data = await state.get_data()
        index = data.get("q_index", 0)
        questions = data.get("questions", [])
        current_question_id = data.get("current_question_id")
        question_start_time_str = data.get("question_start_time")

        if index >= len(questions) or not current_question_id:
            logging.error(f"❌ QUIZ: Некорректные данные вопроса для {question_uuid}")
            del active_questions[question_uuid]
            return

        # Вычисляем время
        time_spent = None
        if question_start_time_str:
            question_start_time = datetime.fromisoformat(question_start_time_str)
            time_spent = int((datetime.now() - question_start_time).total_seconds())

        # Получаем данные текущего вопроса
        current_question_data = questions[index]

        # Сохраняем результат как неправильный ответ (таймаут)
        question_results = data.get("question_results", [])
        question_results.append({
            "question_id": current_question_id,
            "selected_answer_id": None,
            "is_correct": False,
            "time_spent": time_spent,
            "microtopic_number": current_question_data['microtopic_number']
        })

        # Показываем правильный ответ
        current_answer_options = data.get("current_answer_options", [])
        correct_answer = next((opt for opt in current_answer_options if opt['is_correct']), None)

        if correct_answer:
            timeout_message = await bot.send_message(
                chat_id,
                f"⏰ Время вышло!\n\n"
                f"✅ Правильный ответ: {correct_answer['text']}"
            )

            # Добавляем сообщение о таймауте в список для удаления
            data = await state.get_data()
            messages_to_delete = data.get("messages_to_delete", [])
            messages_to_delete.append(timeout_message.message_id)
            await state.update_data(messages_to_delete=messages_to_delete)

        # Переходим к следующему вопросу
        await state.update_data(
            q_index=index + 1,
            question_results=question_results,
            question_answered=False
        )

        # Очищаем из активных вопросов
        del active_questions[question_uuid]

        # Отправляем следующий вопрос или завершаем тест
        await send_next_question(chat_id, state, bot, finish_callback)

    except Exception as e:
        logging.error(f"❌ QUIZ: Ошибка в process_question_timeout_reliable для {question_uuid}: {e}")
        import traceback

        # Очищаем из активных вопросов при ошибке
        if question_uuid in active_questions:
            del active_questions[question_uuid]
